/**
 * Test Strategy Persistence and Configuration
 * 
 * This test verifies that strategy configurations are properly persisted
 * and can be updated even when the SignalEngine is not initialized.
 */

import { PocketOption } from '../main/broker/PocketOption'
import { StrategyFactory } from '../main/core/StrategyFactory'

async function testStrategyPersistence() {
  console.log('🧪 Testing Strategy Persistence and Configuration...\n')

  // Test 1: Create PocketOption instance without starting bot
  console.log('Test 1: Initialize PocketOption without starting bot')
  
  const broker = new PocketOption()
  
  console.log(`✓ PocketOption instance created`)
  console.log(`✓ Signal engine ready: ${broker.isSignalEngineReady()}`)
  console.log(`✓ Initial active strategies: ${broker.getActiveStrategies().join(', ')}\n`)

  // Test 2: Update strategies before bot is started
  console.log('Test 2: Update strategies before bot initialization')
  
  try {
    const newStrategies: StrategyType[] = ['bollinger-rsi', 'threshold']
    broker.updateStrategies(newStrategies)
    
    const activeStrategies = broker.getActiveStrategies()
    console.log(`✓ Successfully updated strategies: ${activeStrategies.join(', ')}`)
    console.log(`✓ Signal engine still not initialized: ${!broker.isSignalEngineReady()}`)
    
    // Verify strategies are persisted
    if (JSON.stringify(activeStrategies.sort()) === JSON.stringify(newStrategies.sort())) {
      console.log(`✓ Strategies correctly persisted in TradeSettings`)
    } else {
      console.log(`✗ Strategy persistence failed`)
    }
    console.log()
  } catch (error) {
    console.error(`✗ Failed to update strategies: ${error}`)
  }

  // Test 3: Update strategy configuration before bot is started
  console.log('Test 3: Update strategy configuration before bot initialization')
  
  try {
    const configUpdate = {
      bollingerRsiConfig: {
        bollingerPeriod: 25,
        rsiPeriod: 10,
        rsiOverbought: 80,
        rsiOversold: 20,
        noiseFilter: true,
        trendConfirmation: true
      }
    }
    
    broker.updateStrategyConfig('bollinger-rsi', configUpdate)
    
    const configs = broker.getStrategyConfigs()
    const bollingerConfig = configs['bollinger-rsi']
    
    console.log(`✓ Successfully updated Bollinger RSI configuration`)
    console.log(`✓ Bollinger Period: ${bollingerConfig?.bollingerRsiConfig?.bollingerPeriod}`)
    console.log(`✓ RSI Period: ${bollingerConfig?.bollingerRsiConfig?.rsiPeriod}`)
    console.log(`✓ RSI Overbought: ${bollingerConfig?.bollingerRsiConfig?.rsiOverbought}`)
    console.log(`✓ Configuration persisted without SignalEngine\n`)
  } catch (error) {
    console.error(`✗ Failed to update strategy configuration: ${error}`)
  }

  // Test 4: Get default configurations
  console.log('Test 4: Get default strategy configurations')
  
  try {
    const defaultThreshold = broker.getDefaultStrategyConfig('threshold')
    const defaultBollingerRsi = broker.getDefaultStrategyConfig('bollinger-rsi')
    
    console.log(`✓ Default threshold config loaded`)
    console.log(`✓ Default Bollinger RSI config loaded`)
    console.log(`✓ Bollinger RSI default period: ${defaultBollingerRsi.bollingerRsiConfig?.bollingerPeriod}`)
    console.log(`✓ Bollinger RSI default RSI period: ${defaultBollingerRsi.bollingerRsiConfig?.rsiPeriod}\n`)
  } catch (error) {
    console.error(`✗ Failed to get default configurations: ${error}`)
  }

  // Test 5: Verify available strategies
  console.log('Test 5: Verify available strategies')
  
  try {
    const availableStrategies = StrategyFactory.getAvailableStrategies()
    
    console.log(`✓ Available strategies: ${availableStrategies.length}`)
    availableStrategies.forEach(strategy => {
      console.log(`  - ${strategy.name}: ${strategy.description}`)
    })
    
    // Check if our new Bollinger RSI strategy is available
    const bollingerRsiStrategy = availableStrategies.find(s => s.type === 'bollinger-rsi')
    if (bollingerRsiStrategy) {
      console.log(`✓ Bollinger RSI strategy is properly registered`)
    } else {
      console.log(`✗ Bollinger RSI strategy not found in available strategies`)
    }
    console.log()
  } catch (error) {
    console.error(`✗ Failed to get available strategies: ${error}`)
  }

  // Test 6: Test multiple strategy configuration updates
  console.log('Test 6: Multiple strategy configuration updates')
  
  try {
    // Add more strategies
    broker.updateStrategies(['threshold', 'bollinger-rsi', 'supertrend'])
    
    // Update threshold strategy config
    broker.updateStrategyConfig('threshold', {
      threshold: 0.025,
      volatilityFilter: true,
      momentumConfirmation: true
    })
    
    // Update supertrend strategy config
    broker.updateStrategyConfig('supertrend', {
      superTrends: [
        { atrPeriod: 12, multiplier: 2.5 },
        { atrPeriod: 16, multiplier: 2.0 }
      ]
    })
    
    const finalConfigs = broker.getStrategyConfigs()
    const finalStrategies = broker.getActiveStrategies()
    
    console.log(`✓ Final active strategies: ${finalStrategies.join(', ')}`)
    console.log(`✓ Threshold config updated: ${finalConfigs.threshold?.threshold}`)
    console.log(`✓ SuperTrend configs: ${finalConfigs.supertrend?.superTrends?.length} periods`)
    console.log(`✓ All configurations persisted successfully\n`)
  } catch (error) {
    console.error(`✗ Failed multiple configuration updates: ${error}`)
  }

  // Test 7: Error handling for invalid strategy
  console.log('Test 7: Error handling for invalid operations')
  
  try {
    // Try to update config for strategy not in active list
    try {
      broker.updateStrategyConfig('back2trend', { minConfidence: 0.8 })
      console.log(`✓ Successfully updated inactive strategy config (allowed for persistence)`)
    } catch (error) {
      console.log(`✗ Unexpected error for inactive strategy: ${error}`)
    }
    
    // Try to get default config for invalid strategy
    try {
      broker.getDefaultStrategyConfig('invalid-strategy' as StrategyType)
      console.log(`✗ Should have thrown error for invalid strategy`)
    } catch (error) {
      console.log(`✓ Correctly threw error for invalid strategy`)
    }
    
    console.log(`✓ Error handling tests completed\n`)
  } catch (error) {
    console.error(`✗ Unexpected error in error handling tests: ${error}`)
  }

  console.log('✅ Strategy Persistence tests completed!')
  console.log('\n📊 Test Summary:')
  console.log('- ✓ Strategy updates without SignalEngine initialization')
  console.log('- ✓ Configuration persistence in TradeSettings')
  console.log('- ✓ Strategy configuration updates')
  console.log('- ✓ Default configuration retrieval')
  console.log('- ✓ Available strategies enumeration')
  console.log('- ✓ Multiple strategy management')
  console.log('- ✓ Error handling for edge cases')
  console.log('\n🎯 The SignalEngine initialization error should now be resolved!')
}

// Run the test if this file is executed directly
if (require.main === module) {
  testStrategyPersistence().catch(console.error)
}

export { testStrategyPersistence }

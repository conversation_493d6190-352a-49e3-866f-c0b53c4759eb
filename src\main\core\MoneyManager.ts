import { Logger } from '../../shared/utils/Logger'
import { sendToLogViewer } from '../utils/LogEmitter'

type TradeStrategy = 'flat' | 'martingale' | 'fibonacci'

interface TradeSession {
  tradeCapital: number
  targetProfit: number
  initialTradeAmount: number
  currentTradeAmount: number
  strategy: TradeStrategy

  // State tracking
  totalTrades: number
  winCount: number
  lossCount: number
  currentProfit: number
  remainingCapital: number
  initialCapital: number
  startTime: Date

  // Fibonacci sequence for strategy
  fibSequence: number[]
  fibIndex: number

  isActive: boolean
}

const logger = Logger.getContextLogger('MONEY')

class MoneyManager {
  private accountBalance: number
  private session: TradeSession | null = null
  private sessionHistory: Array<{
    profitable: boolean
    profit: number
    trades: number
    wins: number
    losses: number
    strategy: TradeStrategy
    endTime: Date
  }> = []

  constructor(initialBalance: number = 1000) {
    this.accountBalance = initialBalance
  }

  startSession(
    tradeCapital: number,
    targetProfit: number,
    initialTradeAmount: number,
    strategy: TradeStrategy = 'flat'
  ): void {
    if (tradeCapital <= 0 || initialTradeAmount <= 0 || targetProfit <= 0) {
      throw new Error('All parameters must be positive numbers.')
    }
    if (initialTradeAmount > tradeCapital) {
      throw new Error('Initial trade amount cannot exceed trade capital.')
    }
    if (tradeCapital > this.accountBalance) {
      throw new Error('Insufficient account balance.')
    }

    this.session = {
      tradeCapital,
      targetProfit,
      initialTradeAmount,
      currentTradeAmount: initialTradeAmount,
      strategy,
      totalTrades: 0,
      winCount: 0,
      lossCount: 0,
      currentProfit: 0,
      remainingCapital: tradeCapital,
      initialCapital: tradeCapital,
      startTime: new Date(),
      fibSequence: [1, 1],
      fibIndex: 0,
      isActive: true
    }

    this.accountBalance -= tradeCapital
  }

  placeTrade(win: boolean, profitPercent?: number): void {
    if (!this.session || !this.session.isActive) {
      logger.warn(`No active session to place trade.`)
      return
    }

    const profitPercentage = profitPercent ? this.normalizePercent(profitPercent) : undefined

    const s = this.session
    const amount = s.currentTradeAmount

    // Check if there's enough capital for the next trade
    if (amount > s.remainingCapital) {
      logger.error(
        `Insufficient capital for trade. Required: ${amount}, Available: ${s.remainingCapital}`
      )
      // this.endSession()
      return
    }

    s.remainingCapital -= amount
    s.totalTrades++

    if (win) {
      s.winCount++
      // Use actual profit if provided, otherwise fallback to 85% calculation
      const profit = profitPercentage !== undefined ? amount * profitPercentage : amount * 0.85
      s.currentProfit += profit
      // s.remainingCapital += amount + profit

      // Reset trade amount for next trade
      if (s.strategy === 'fibonacci') s.fibIndex = 0
      s.currentTradeAmount = s.initialTradeAmount
      s.remainingCapital += profit
    } else {
      s.lossCount++

      s.currentProfit -= amount
      // For losses, the trade amount is already deducted from remainingCapital (line 102)
      // We don't need to modify currentProfit separately since the loss is implicit
      // in the fact that we lost the trade amount and gained nothing back

      this.applyLossStrategy(s)
    }

    // Stop if target or capital depleted
    if (s.currentProfit >= s.targetProfit || s.remainingCapital <= s.currentTradeAmount) {
      logger.info(
        `Session stop condition met - Target reached: ${s.currentProfit >= s.targetProfit}, Insufficient capital: ${s.remainingCapital <= s.currentTradeAmount}`
      )
      // this.endSession()
    }
  }

  endSession(): void {
    if (!this.session) {
      logger.warn(`No active session to end.`)
      return
    }

    logger.info(`Ending session: ${JSON.stringify(this.session, null, 2)}`)
    const isProfitable = this.session.currentProfit >= this.session.targetProfit
    sendToLogViewer(
      `🎓 Session Ended: ${isProfitable ? `<span class="text-green-400">Session is profitable</span>` : `<span class="text-red-400">Session is not profitable</span>`}`,
      {
        type: 'info',
        time: new Date()
      }
    )

    this.sessionHistory.push({
      profitable: this.session.currentProfit >= this.session.targetProfit,
      profit: this.session.currentProfit,
      trades: this.session.totalTrades,
      wins: this.session.winCount,
      losses: this.session.lossCount,
      strategy: this.session.strategy,
      endTime: new Date()
    })

    this.accountBalance += this.session.remainingCapital
    this.session.isActive = false
    this.session = null
  }

  getAccountBalance(): number {
    return this.accountBalance
  }

  getSessionHistory(): Array<{
    profitable: boolean
    profit: number
    trades: number
    wins: number
    losses: number
    strategy: TradeStrategy
    endTime: Date
  }> {
    return this.sessionHistory
  }

  isSessionActive(): boolean {
    return !!this.session?.isActive
  }

  getCurrentSession(): TradeSession | null {
    return this.session
  }

  getSessionStatus(): MoneyManagementStatus | null {
    if (!this.session) {
      logger.warn(`No active session to get status.`)
      return null
    }

    const successRate =
      this.session.totalTrades > 0 ? (this.session.winCount / this.session.totalTrades) * 100 : 0

    return {
      isSessionActive: this.session.isActive,
      currentCapital: this.session.remainingCapital,
      currentProfit: this.session.currentProfit,
      targetProfit: this.session.targetProfit,
      initialCapital: this.session.initialCapital,
      nextTradeAmount: this.session.currentTradeAmount,
      totalTrades: this.session.totalTrades,
      winCount: this.session.winCount,
      lossCount: this.session.lossCount,
      successRate: Math.round(successRate * 100) / 100
    }
  }

  shouldStopSession(): { shouldStop: boolean; reason?: string } {
    if (!this.session || !this.session.isActive) {
      return { shouldStop: false }
    }

    // Check if next trade amount exceeds remaining capital
    if (this.session.currentTradeAmount >= this.session.remainingCapital) {
      return {
        shouldStop: true,
        reason: `Insufficient capital for next trade. Required: $${this.session.currentTradeAmount}, Available: $${this.session.remainingCapital.toFixed(2)}`
      }
    }

    // Check if target profit reached
    if (this.session.currentProfit >= this.session.targetProfit) {
      return {
        shouldStop: true,
        reason: `Target profit of $${this.session.targetProfit} reached. Current profit: $${this.session.currentProfit.toFixed(2)}`
      }
    }

    // Check if capital depleted to 10% of original
    const minCapital = this.session.initialCapital * 0.1
    if (this.session.remainingCapital <= minCapital) {
      return {
        shouldStop: true,
        reason: `Capital depleted to 10% of original amount. Remaining: $${this.session.remainingCapital.toFixed(2)}`
      }
    }

    return { shouldStop: false }
  }

  getSessionResult(): SessionResult | null {
    if (!this.session) {
      logger.warn(`No active session to get result.`)
      return null
    }

    const successRate =
      this.session.totalTrades > 0 ? (this.session.winCount / this.session.totalTrades) * 100 : 0

    let endReason: 'target_reached' | 'capital_depleted' | 'manual_stop' = 'manual_stop'

    if (this.session.currentProfit >= this.session.targetProfit) {
      endReason = 'target_reached'
    } else if (this.session.remainingCapital <= this.session.initialCapital * 0.1) {
      endReason = 'capital_depleted'
    } else if (this.session.currentTradeAmount >= this.session.remainingCapital) {
      endReason = 'capital_depleted'
    }

    return {
      profitable: this.session.currentProfit > 0,
      totalProfit: this.session.currentProfit,
      totalTrades: this.session.totalTrades,
      winCount: this.session.winCount,
      lossCount: this.session.lossCount,
      successRate: Math.round(successRate * 100) / 100,
      strategy: this.session.strategy,
      endReason,
      startTime: this.session.startTime,
      endTime: new Date(),
      initialCapital: this.session.initialCapital,
      finalCapital: this.session.remainingCapital
    }
  }

  validateTradeCapital(tradeCapital: number): { isValid: boolean; error?: string } {
    if (tradeCapital <= 0) {
      return { isValid: false, error: 'Trade capital must be greater than 0' }
    }

    if (tradeCapital > this.accountBalance) {
      return {
        isValid: false,
        error: `Insufficient account balance. Available: $${this.accountBalance.toFixed(2)}, Required: $${tradeCapital.toFixed(2)}`
      }
    }

    return { isValid: true }
  }

  private normalizePercent(value: number): number {
    // If value is greater than 1, assume it's in percentage format (e.g., 90)
    // Convert to decimal format (e.g., 0.9)
    return value > 1 ? value / 100 : value
  }

  private applyLossStrategy(session: TradeSession): void {
    if (session.strategy === 'martingale') {
      session.currentTradeAmount *= 2
    } else if (session.strategy === 'fibonacci') {
      session.fibIndex++
      if (session.fibIndex >= session.fibSequence.length) {
        const next =
          session.fibSequence[session.fibIndex - 1] + session.fibSequence[session.fibIndex - 2]
        session.fibSequence.push(next)
      }
      session.currentTradeAmount =
        session.initialTradeAmount * session.fibSequence[session.fibIndex]
    }
  }
}

export default MoneyManager

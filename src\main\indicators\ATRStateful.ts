export class ATRStateful {
  private period: number
  private trueRanges: number[] = []
  private atr: number | null = null
  private isInitialized = false

  constructor(period: number) {
    this.period = period
  }

  // A helper to calculate the first True Range
  static calculateTR(candle: { high: number; low: number }, prevClose: number): number {
    return Math.max(
      candle.high - candle.low,
      Math.abs(candle.high - prevClose),
      Math.abs(candle.low - prevClose)
    )
  }

  update(candle: { high: number; low: number; close: number }, prevClose: number): number | null {
    const tr = ATRStateful.calculateTR(candle, prevClose)

    if (!this.isInitialized) {
      this.trueRanges.push(tr)
      if (this.trueRanges.length === this.period) {
        // First ATR is a simple average of the first 'period' TRs
        this.atr = this.trueRanges.reduce((sum, val) => sum + val, 0) / this.period
        this.isInitialized = true
        this.trueRanges = [] // Clear for memory efficiency
      }
    } else {
      // Subsequent ATRs use <PERSON>'s smoothing (EMA)
      this.atr = (this.atr! * (this.period - 1) + tr) / this.period
    }
    return this.atr
  }

  getPeriod(): number {
    return this.period
  }

  get value(): number | null {
    return this.atr
  }
}

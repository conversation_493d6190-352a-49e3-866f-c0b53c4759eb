/**
 * Calculate Average True Range (ATR) over a given period using a simple moving average
 * @param candles  Array of Candle objects, must contain at least period + 1 entries
 * @param period   Number of bars for ATR calculation (e.g., 14)
 * @returns        ATR value in price units
 * @throws         Error if there are not enough candles
 */
export function ATR(candles: Candle[], period: number): number {
  if (candles.length < period + 1) {
    throw new Error(
      `Not enough candles to calculate ATR (need ${period + 1}, got ${candles.length})`
    )
  }

  // Compute True Range (TR) for each of the last `period` bars
  const trs: number[] = []
  for (let i = candles.length - period; i < candles.length; i++) {
    const curr = candles[i]
    const prev = candles[i - 1]
    const highLow = curr.high - curr.low
    const highPrevClose = Math.abs(curr.high - prev.close)
    const lowPrevClose = Math.abs(curr.low - prev.close)
    const tr = Math.max(highLow, highPrevClose, lowPrevClose)
    trs.push(tr)
  }

  // Simple moving average of TRs
  const sum = trs.reduce((acc, v) => acc + v, 0)
  return sum / trs.length
}
